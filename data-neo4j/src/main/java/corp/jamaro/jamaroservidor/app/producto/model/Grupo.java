package corp.jamaro.jamaroservidor.app.producto.model;

import corp.jamaro.jamaroservidor.app.producto.model.enums.TipoCategoria;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;
import java.util.Set;

@Data
@Node
public class Grupo {
    @Id
    private String id;//por ahora va ser la abreviación de la Categoria de la db antigua

    private String nombrePrincipal;

    private TipoCategoria tipo; //categoria, sub categoria, familia, etc

    @Relationship(type = "CON_NOMBRE_GRUPO")
    private Set<NombreGrupo> nombresGrupo; //asegurarnos de que cada NombreCategoria le pertenezca solo a una Categoria

    @Relationship(type = "CON_SUB_GRUPO")
    private Set<Grupo> subGrupos;

    @Relationship(type = "CON_FILTRO")
    private List<GrupoFiltroRelation> filtros;//aquí es list xq se puede repetir un filtro

}
