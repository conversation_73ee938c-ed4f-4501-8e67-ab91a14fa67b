package corp.jamaro.jamaroservidor.app.ventas.model.gui;

import corp.jamaro.jamaroservidor.app.producto.model.Filtro;
import corp.jamaro.jamaroservidor.app.producto.model.enums.TipoBusqueda;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.RelationshipId;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

import java.util.UUID;

@Data
@RelationshipProperties
public class FiltroDatoRellenado {
    @RelationshipId
    @Id
    private String id;
    @TargetNode
    private Filtro filtro;

    // Indica la fila en la “matriz” de filtros.
    private Integer fila;

    // Indica la columna en la “matriz” de filtros.
    private Integer columna;

    // Dato ingresado (o vacío) para este filtro.
    private String dato;

    private TipoBusqueda tipoBusqueda;
}
