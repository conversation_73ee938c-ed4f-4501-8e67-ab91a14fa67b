package corp.jamaro.jamaroservidor.app.ventas.model.gui;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.RelationshipId;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

import java.time.Instant;
import java.util.UUID;

@Data
@RelationshipProperties
public class ToSaleGuiRelation {
    @RelationshipId
    @Id
    private String id;

    @TargetNode
    private SaleGui saleGui;

    private Integer ordenPresentacion;//orden en la GUI del user.

    private Float dividerX= 0.81F;
    private Float dividerY= 0.21F;

    private Instant agregadoEl = Instant.now();

}
