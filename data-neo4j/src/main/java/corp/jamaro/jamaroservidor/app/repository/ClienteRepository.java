package corp.jamaro.jamaroservidor.app.repository;

import corp.jamaro.jamaroservidor.app.model.Cliente;

import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.UUID;

@Repository
public interface ClienteRepository extends ReactiveNeo4jRepository<Cliente, UUID> {

    /**
     * Busca clientes usando expresión regular en nombre, apellido o razónSocial.
     * Búsqueda insensible a mayúsculas y minúsculas, sin importar posición.
     * Limitado a 30 resultados para optimizar rendimiento.
     *
     * @param regex Expresión regular para la búsqueda
     * @return Flux de hasta 30 clientes que coinciden con la búsqueda
     */
    @Query("""
           MATCH (c:Cliente)
           WHERE c.estado = true
           AND (c.nombre =~ $regex OR c.apellido =~ $regex OR c.razonSocial =~ $regex)
           RETURN c
           LIMIT 30
           """)
    Flux<Cliente> findByNameRegex(String regex);

    /**
     * Busca clientes por coincidencia exacta en dni, ruc o otroDocumento.
     * Búsqueda insensible a mayúsculas y minúsculas.
     *
     * @param regex Expresión regular para coincidencia exacta del documento
     * @return Flux de clientes que coinciden exactamente con el documento
     */
    @Query("""
           MATCH (c:Cliente)
           WHERE c.estado = true
           AND (c.dni =~ $regex OR c.ruc =~ $regex OR c.otroDocumento =~ $regex)
           RETURN c
           """)
    Flux<Cliente> findByDocumentExact(String regex);
}
