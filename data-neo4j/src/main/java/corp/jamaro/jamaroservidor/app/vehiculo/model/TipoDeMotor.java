package corp.jamaro.jamaroservidor.app.vehiculo.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;
import java.util.UUID;


@Data
@Node
public class TipoDeMotor {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String tipoMotor;//gasolina, di<PERSON><PERSON>-petr<PERSON><PERSON>, h<PERSON><PERSON><PERSON> (gasolina-eléctrico), h<PERSON><PERSON><PERSON> (diésel-eléctrico), eléctrico
    private Instant creadoActualizado = Instant.now();
}
