package corp.jamaro.jamaroservidor.app.ventas.repository.gui;

import corp.jamaro.jamaroservidor.app.ventas.model.dto.UniversalSaleGuiDto;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.UniversalSaleGui;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;
import java.util.Set;

@Repository
public interface UniversalSaleGuiRepository extends ReactiveNeo4jRepository<UniversalSaleGui, UUID> {

    /**
     * Consulta que retorna únicamente el identificador de la UniversalSaleGui asociada al User.
     */
    @Query("""
        MATCH (u:UniversalSaleGui)-[:PERTENECE_AL_USER]->(usr:User)
        WHERE usr.id = $userId
        RETURN u.id AS id
    """)
    Mono<UUID> findGuiIdByUserId(UUID userId);

    /**
     * Consulta optimizada que retorna directamente un DTO con los datos necesarios de UniversalSaleGui
     * sin cargar toda la entidad y sus relaciones.
     *
     * @param id ID de la UniversalSaleGui
     * @return DTO con los datos esenciales
     */
    @Query("""
        MATCH (u:UniversalSaleGui)-[:PERTENECE_AL_USER]->(usr:User)
        WHERE u.id = $id
        OPTIONAL MATCH (u)-[:CON_MAINSALEGUI]->(m:MainSaleGui)
        RETURN u.id AS id,
               usr.username AS userUsername,
               m.id AS mainSaleGuiId,
               u.auditingMainSales AS auditingMainSales,
               u.guiConfig AS guiConfig,
               u.version AS version
    """)
    Mono<UniversalSaleGuiDto> findDtoById(UUID id);

    /**
     * Consulta optimizada que retorna directamente un DTO con los datos necesarios de UniversalSaleGui
     * para un usuario específico, sin cargar toda la entidad y sus relaciones.
     *
     * @param userId ID del usuario
     * @return DTO con los datos esenciales
     */
    @Query("""
        MATCH (u:UniversalSaleGui)-[:PERTENECE_AL_USER]->(usr:User)
        WHERE usr.id = $userId
        OPTIONAL MATCH (u)-[:CON_MAINSALEGUI]->(m:MainSaleGui)
        RETURN u.id AS id,
               usr.username AS userUsername,
               m.id AS mainSaleGuiId,
               u.auditingMainSales AS auditingMainSales,
               u.guiConfig AS guiConfig,
               u.version AS version
    """)
    Mono<UniversalSaleGuiDto> findDtoByUserId(UUID userId);

    /**
     * Actualiza la configuración (guiConfig) de la UniversalSaleGui y retorna el DTO actualizado.
     *
     * @param guiId ID de la UniversalSaleGui
     * @param guiConfig Nueva configuración en formato JSON
     * @return DTO con los datos actualizados
     */
    @Query("""
        MATCH (u:UniversalSaleGui)
        WHERE u.id = $guiId
        SET u.guiConfig = $guiConfig
        WITH u
        OPTIONAL MATCH (u)-[:PERTENECE_AL_USER]->(usr:User)
        OPTIONAL MATCH (u)-[:CON_MAINSALEGUI]->(m:MainSaleGui)
        RETURN u.id AS id,
               usr.username AS userUsername,
               m.id AS mainSaleGuiId,
               u.auditingMainSales AS auditingMainSales,
               u.guiConfig AS guiConfig,
               u.version AS version
    """)
    Mono<UniversalSaleGuiDto> updateGuiConfigAndReturnDto(UUID guiId, String guiConfig);

    /**
     * Agrega un MainSaleGui a la lista de auditoría y retorna el DTO actualizado.
     *
     * @param guiId ID de la UniversalSaleGui
     * @param mainSaleGuiId ID del MainSaleGui a agregar a la auditoría
     * @return DTO con los datos actualizados
     */
    @Query("""
        MATCH (u:UniversalSaleGui)
        WHERE u.id = $guiId
        WITH u,
             CASE
               WHEN u.auditingMainSales IS NULL THEN []
               ELSE u.auditingMainSales
             END AS existingAudits
        SET u.auditingMainSales =
            CASE
              WHEN $mainSaleGuiId IN existingAudits THEN existingAudits
              ELSE existingAudits + $mainSaleGuiId
            END
        WITH u
        OPTIONAL MATCH (u)-[:PERTENECE_AL_USER]->(usr:User)
        OPTIONAL MATCH (u)-[:CON_MAINSALEGUI]->(m:MainSaleGui)
        RETURN u.id AS id,
               usr.username AS userUsername,
               m.id AS mainSaleGuiId,
               u.auditingMainSales AS auditingMainSales,
               u.guiConfig AS guiConfig,
               u.version AS version
    """)
    Mono<UniversalSaleGuiDto> addAuditedMainSaleAndReturnDto(UUID guiId, UUID mainSaleGuiId);

    /**
     * Remueve un MainSaleGui de la lista de auditoría y retorna el DTO actualizado.
     *
     * @param guiId ID de la UniversalSaleGui
     * @param mainSaleGuiId ID del MainSaleGui a remover de la auditoría
     * @return DTO con los datos actualizados
     */
    @Query("""
        MATCH (u:UniversalSaleGui)
        WHERE u.id = $guiId
        WITH u,
             CASE
               WHEN u.auditingMainSales IS NULL THEN []
               ELSE u.auditingMainSales
             END AS existingAudits
        SET u.auditingMainSales = [audit IN existingAudits WHERE audit <> $mainSaleGuiId]
        WITH u
        OPTIONAL MATCH (u)-[:PERTENECE_AL_USER]->(usr:User)
        OPTIONAL MATCH (u)-[:CON_MAINSALEGUI]->(m:MainSaleGui)
        RETURN u.id AS id,
               usr.username AS userUsername,
               m.id AS mainSaleGuiId,
               u.auditingMainSales AS auditingMainSales,
               u.guiConfig AS guiConfig,
               u.version AS version
    """)
    Mono<UniversalSaleGuiDto> removeAuditedMainSaleAndReturnDto(UUID guiId, UUID mainSaleGuiId);
}
