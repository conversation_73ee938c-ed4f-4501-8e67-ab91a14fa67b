package corp.jamaro.jamaroservidor.app.producto.model;

import corp.jamaro.jamaroservidor.app.model.file.relation.ToBucketFileRelation;
import corp.jamaro.jamaroservidor.app.producto.model.enums.TipoFiltro;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Set;
import java.util.UUID;

@Data
@Node
public class Filtro {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String nombreFiltro;//interior, exterior, altura, terminal, terminal|diametro|conico ( filtro compuesto donde "|" es el separador para luego interpretar en el frontend)

    @Relationship(type = "CON_FILTRO_FILE")
    private Set<ToBucketFileRelation> files;// Generalmente van a ser Imagenes, animaciones, modelos 3d, etc.

    private TipoFiltro tipo;
    // , numérico (textField), opción multiple (combobox), cadena texto (autocomplete), DICOTOMICO (dos radio buttons o dos checkboxes),
    // compuesto( ejemp terminal|diametro|conico)



}
