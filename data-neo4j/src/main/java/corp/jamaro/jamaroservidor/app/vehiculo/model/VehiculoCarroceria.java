package corp.jamaro.jamaroservidor.app.vehiculo.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class VehiculoCarroceria {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String carroceria;// (sedán, SUV, hatchback, camioneta, etc.).
    private String descripcion;
    private Instant creadoActualizado = Instant.now();
}
