package corp.jamaro.jamaroservidor.app.vehiculo.model.draft;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.model.file.relation.ToBucketFileRelation;
import corp.jamaro.jamaroservidor.app.vehiculo.model.*;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class VehiculoDraft {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "CON_NOMBRE_DE_VEHICULO")
    private Set<VehiculoNombre> nombres;

    @Relationship(type = "CON_VEHICULO_MARCA")
    private VehiculoMarca vehiculoMarca;

    @Relationship(type = "CON_VEHICULO_MODELO")
    private VehiculoModelo vehiculoModelo;

    @Relationship(type = "DEL_ANIO")
    private Set<VehiculoAnio> vehiculoAnios;

    @Relationship(type= "CON_MOTOR")
    private VehiculoMotor vehiculoMotor;

    @Relationship(type = "CON_CILINDRADA")
    private VehiculoCilindrada vehiculoCilindrada; // (e.g., 1.5L, 2.0L).

    @Relationship(type = "CON_VEHICULO_VERSION")
    private VehiculoVersion vehiculoVersion;//(GL, XLE, Sport, basic, full, etc.).

    @Relationship(type = "CON_VEHICULO_CARROCERIA")
    private VehiculoCarroceria vehiculoCarroceria;// (sedán, SUV, hatchback, camioneta, etc.).

    @Relationship(type = "CON_TIPO_DE_TRACCION")
    private VehiculoTraccion vehiculoTraccion;//Tracción delantera, trasera, 4x4, AWD, etc.

    @Relationship(type = "CON_TIPO_DE_TRANSMISION")
    private VehiculoTransmision vehiculoTransmision;//mecánica, automática, cvt, etc

    @Relationship(type = "CON_FILE")
    private Set<ToBucketFileRelation> files;

    private Instant creadoActualizado;


    @Relationship(type = "CREADO_ACTUALIZADO_POR_EL_USUARIO")
    private User user;

}
