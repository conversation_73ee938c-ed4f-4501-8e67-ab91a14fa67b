package corp.jamaro.jamaroservidor.app.producto.model;

import corp.jamaro.jamaroservidor.app.model.file.relation.ToBucketFileRelation;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Set;
import java.util.UUID;

@Data
@Node
public class Item {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String codCompuesto;//código interno que se forma con ciertos atributos de la lógica de negocio

    private String descripcion;

    private Double precioCostoPromedio;//un promedio del precio costo según los precio de compra hechos a diferentes proveedores.
    private Double precioVentaBase;//precio minimo de venta.
    private Double precioVentaPublico;//precio de venta que aparecerá en el sistema

    private Double stockTotal;
    private Double stockDeSeguridad;

    @Relationship(type = "ITEM_PARTE_DEL_PRODUCTO")
    private Set<Producto> productos;

    @Relationship(type = "CON_CODIGO_FABRICA")
    private Set<CodigoFabrica> codigosFabrica;

    @Relationship(type = "CON_MARCA")
    private Marca marca;

    @Relationship(type = "CON_ATRIBUTO")
    private Set<Atributo> atributos;

    @Relationship(type = "CON_CODIGO_FABRICA")
    private Set<Ubicacion> ubicaciones;

    @Relationship(type = "CON_ITEM_IMAGEN")
    private Set<ToBucketFileRelation> imagenes;

    // borrar este luego
    private String anotacionesOld;
    private String codProductoOld;

    //implementar luego su respectivo nodo
    private String vehiculoOld;
    private String codFabricaOld;


}
