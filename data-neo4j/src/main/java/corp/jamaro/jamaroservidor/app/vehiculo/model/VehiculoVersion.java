package corp.jamaro.jamaroservidor.app.vehiculo.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class VehiculoVersion {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String version;//(GL, XLE, Sport, basic, full, etc.).
    private String descripcion;

    private Instant creadoActualizado = Instant.now();
}

/*
Un coche básico que se llama, por ejemplo, “Modelo X”. Ese mismo coche puede tener distintas presentaciones con más o menos accesorios.
Entonces el fabricante les da nombres o siglas específicas, como GL, XLE, Sport o similares, para mostrar cuál es la “receta” exacta de ese coche. Cada versión puede tener cosas distintas, por ejemplo:

GL: Podría ser la versión más básica, con características estándar.
XLE: Una versión más completa, con aire acondicionado automático, asientos de mejor calidad, o más tecnología en el tablero.
Sport: Una versión orientada a la deportividad, con un motor más potente, suspensión más firme o un diseño más agresivo.
 */