package corp.jamaro.jamaroservidor.app.producto.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.UUID;

@Data
@Node
public class Atributo {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "PERTENECE_AL_FILTRO")
    private Filtro filtro;

    private String datoString;
    private Double datoNumerico;

    private String datoDicotomico;//solo puede tomar dos valores y la incertidumbre que sería null.
    //datos dicotomicos ejemplo con-abs, sin-abs; delantero, posterior; izquierdo, derecho; imantado, no-imantado; con-tope, sin-tope; con-seguro, sin-seguro

    private String datoCompuesto;//ejemplo para mangueras de alternador y freno terminal|diametro|conico = macho|10|cono-da

//    @Relationship(type = "CON_ATRIBUTO_DATO_FILE")
//    private Set<ToBucketFileRelation> datoFile;// Generalmente van a ser Imagenes, pero pueden ser animaciones, modelos 3d etc.
}
