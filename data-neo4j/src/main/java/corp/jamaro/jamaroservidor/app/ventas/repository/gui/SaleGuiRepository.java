package corp.jamaro.jamaroservidor.app.ventas.repository.gui;

import corp.jamaro.jamaroservidor.app.ventas.model.gui.SaleGui;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.UUID;

@Repository
public interface SaleGuiRepository extends ReactiveNeo4jRepository<SaleGui, UUID> {

    /**
     * Carga un SaleGui por su ID con su relación a CollaborativeRoom.
     *
     * @param saleGuiId El UUID del SaleGui a cargar.
     * @return Mono que emite el SaleGui con su relación o vacío si no se encuentra.
     */
    @Query("""
           MATCH (s:SaleGui) WHERE s.id = $saleGuiId
           OPTIONAL MATCH (s)-[r:CON_COLLABORATIVE_ROOM]->(c:CollaborativeRoom)
           RETURN s, r, c
           """)
    Mono<SaleGui> findSaleGuiWithCollaborativeRoomById(UUID saleGuiId);

    /**
     * Carga un SaleGui por su ID con todas sus relaciones (CollaborativeRoom, SearchProductGui, Sale).
     * Esta consulta es más eficiente que usar findById ya que carga exactamente lo necesario.
     * Usa collect() para agrupar los resultados y evitar duplicados.
     *
     * @param saleGuiId El UUID del SaleGui a cargar.
     * @return Mono que emite el SaleGui con sus relaciones o vacío si no se encuentra.
     */
    @Query("""
           MATCH (s:SaleGui) WHERE s.id = $saleGuiId
           OPTIONAL MATCH (s)-[r1:CON_COLLABORATIVE_ROOM]->(c:CollaborativeRoom)
           OPTIONAL MATCH (s)-[r2:CON_BUSQUEDA_PRODUCTOS]->(sp:SearchProductGui)
           OPTIONAL MATCH (s)-[r3:CON_VENTA]->(sale:Sale)
           RETURN s, r1, c, collect(r2), collect(sp), r3, sale
           """)
    Mono<SaleGui> findSaleGuiWithAllRelationsById(UUID saleGuiId);

    /**
     * Obtiene el iniciadaPor de un SaleGui a través de su relación con CollaborativeRoom.
     *
     * @param saleGuiId El UUID del SaleGui.
     * @return Mono que emite el username del iniciador o vacío si no se encuentra.
     */
    @Query("""
           MATCH (s:SaleGui)-[:CON_COLLABORATIVE_ROOM]->(c:CollaborativeRoom)
           WHERE s.id = $saleGuiId
           RETURN c.iniciadaPor
           """)
    Mono<String> findIniciadaPorBySaleGuiId(UUID saleGuiId);

    /**
     * Establece una relación entre un SaleGui y un CollaborativeRoom.
     *
     * @param saleGuiId ID del SaleGui
     * @param collaborativeRoomId ID del CollaborativeRoom
     * @return Mono que completa cuando la operación termina
     */
    @Query("""
           MATCH (s:SaleGui) WHERE s.id = $saleGuiId
           MATCH (c:CollaborativeRoom) WHERE c.id = $collaborativeRoomId
           CREATE (s)-[r:CON_COLLABORATIVE_ROOM]->(c)
           """)
    Mono<Void> addCollaborativeRoomRelation(UUID saleGuiId, UUID collaborativeRoomId);

    /**
     * Establece una relación entre un SaleGui y un SearchProductGui.
     *
     * @param saleGuiId ID del SaleGui
     * @param searchProductGuiId ID del SearchProductGui
     * @return Mono que completa cuando la operación termina
     */
    @Query("""
           MATCH (s:SaleGui) WHERE s.id = $saleGuiId
           MATCH (sp:SearchProductGui) WHERE sp.id = $searchProductGuiId
           CREATE (s)-[r:CON_BUSQUEDA_PRODUCTOS]->(sp)
           """)
    Mono<Void> addSearchProductGuiRelation(UUID saleGuiId, UUID searchProductGuiId);

    /**
     * Elimina la relación entre un SaleGui y un SearchProductGui.
     *
     * @param saleGuiId ID del SaleGui
     * @param searchProductGuiId ID del SearchProductGui
     * @return Mono que completa cuando la operación termina
     */
    @Query("""
           MATCH (s:SaleGui)-[r:CON_BUSQUEDA_PRODUCTOS]->(sp:SearchProductGui)
           WHERE s.id = $saleGuiId AND sp.id = $searchProductGuiId
           DELETE r
           """)
    Mono<Void> removeSearchProductGuiRelation(UUID saleGuiId, UUID searchProductGuiId);

    /**
     * Establece una relación entre un SaleGui y un Sale.
     *
     * @param saleGuiId ID del SaleGui
     * @param saleId ID del Sale
     * @return Mono que completa cuando la operación termina
     */
    @Query("""
           MATCH (s:SaleGui) WHERE s.id = $saleGuiId
           MATCH (sale:Sale) WHERE sale.id = $saleId
           CREATE (s)-[r:CON_VENTA]->(sale)
           """)
    Mono<Void> addSaleRelation(UUID saleGuiId, UUID saleId);

    /**
     * Actualiza la fecha de creación de un SaleGui.
     *
     * @param saleGuiId ID del SaleGui
     * @param createdAt Nueva fecha de creación
     * @return Mono que completa cuando la operación termina
     */
    @Query("""
           MATCH (s:SaleGui) WHERE s.id = $saleGuiId
           SET s.createdAt = $createdAt
           """)
    Mono<Void> updateCreatedAt(UUID saleGuiId, Instant createdAt);
}
