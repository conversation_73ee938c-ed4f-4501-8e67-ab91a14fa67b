package corp.jamaro.jamaroservidor.app.vehiculo.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class VehiculoCilindrada {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String cilindrada;// (e.g., 1000, 1500, 1.5, 2.0).
    private Instant creadoActualizado = Instant.now();
}
