package corp.jamaro.jamaroservidor.app.vehiculo.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class VehiculoMotor {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String motor;//4e, 5e, 3l

    @Relationship(value = "CON_TIPO_DE_MOTOR")
    private TipoDeMotor tipoDeMotor;//gasolinero, diesel,hibrido(gasolina-electrico), hibrido(petroleo-electrico), electrico

    private Instant creadoActualizado = Instant.now();
}
