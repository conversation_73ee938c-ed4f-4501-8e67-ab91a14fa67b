package corp.jamaro.jamaroservidor.app.vehiculo.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class VehiculoMarca {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String marca;//toyota, nissan, etc

    @Relationship(value = "CON_MODELO")
    Set<VehiculoModelo> modelos;

    @Relationship(value = "CON_MOTOR")
    Set<VehiculoMotor> motores;

    @Relationship(value = "CON_VERSION")
    Set<VehiculoVersion> versiones;

    private Instant creadoActualizado = Instant.now();

}