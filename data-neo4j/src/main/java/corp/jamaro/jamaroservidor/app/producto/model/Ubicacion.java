package corp.jamaro.jamaroservidor.app.producto.model;

import corp.jamaro.jamaroservidor.app.model.file.relation.ToBucketFileRelation;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Set;
import java.util.UUID;

@Data
@Node
public class Ubicacion {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String nombre;// sera codificado en base a estante piso separación

    private TipoUbicacion tipo;

    @Relationship(type = "UBICACION_CON_IMAGEN")
    private Set<ToBucketFileRelation> imagenes;

    @Relationship(type = "PERTENECE_AL_ESTANTE")
    private Ubicacion estante;

    //private Boolean isPrincipal = true;// para saber si es la fuente principal de donde se descontará primero el stock

    public enum TipoUbicacion {
        ESTANTE,
        SEPARACION
    }
}
