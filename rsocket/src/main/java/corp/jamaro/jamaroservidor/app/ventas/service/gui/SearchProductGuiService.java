package corp.jamaro.jamaroservidor.app.ventas.service.gui;

import com.fasterxml.jackson.databind.ObjectMapper;
import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.model.Producto;
import corp.jamaro.jamaroservidor.app.producto.model.enums.TipoBusqueda;
import corp.jamaro.jamaroservidor.app.producto.repository.GrupoRepository;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import corp.jamaro.jamaroservidor.app.producto.repository.ProductoRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.FiltroDatoRellenado;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroservidor.app.ventas.repository.gui.SearchProductGuiRepository;
import corp.jamaro.jamaroservidor.app.producto.model.GrupoFiltroRelation;
import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.ReactiveNeo4jClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SearchProductGuiService {

    // Repositorios y dependencias existentes
    private final SearchProductGuiRepository searchRepo;
    private final GrupoRepository grupoRepo;
    private final ProductoRepository productoRepository;
    private final ItemRepository itemRepository;

    // Cliente reactivo para construir queries dinámicas en Neo4j
    private final ReactiveNeo4jClient neo4jClient;
    // ObjectMapper de Jackson para convertir el mapa a Producto
    private final ObjectMapper objectMapper;

    // Emisor para notificar actualizaciones vía RSocket
    private final Sinks.Many<SearchProductGui> updateSink = Sinks.many().multicast().directBestEffort();

    /**
     * Se suscribe a los cambios de un SearchProductGui.
     * Devuelve el objeto inicial y luego las actualizaciones.
     */
    public Flux<SearchProductGui> subscribeToChanges(UUID id) {
        // Busca el SearchProductGui por id o lanza error si no existe
        Mono<SearchProductGui> initial = searchRepo.findById(id)
                .switchIfEmpty(Mono.error(new IllegalStateException("No existe SearchProductGui con id=" + id)));
        // Emite actualizaciones únicamente para el id solicitado
        Flux<SearchProductGui> updates = updateSink.asFlux()
                .filter(gui -> gui.getId().equals(id));
        return initial.concatWith(updates);
    }

    /**
     * Retorna los productos según los criterios de búsqueda del SearchProductGui.
     * Si se especifica alguno de los filtros (idGrupo, descripcion, codProductoOld,
     * codFabricaOld (ahora usando la relación PRODUCTO_CON_CODIGO_FABRICA),
     * vehiculoSearch (ahora usando las relaciones PRODUCTO_PARA_EL_VEHICULO y CON_NOMBRE_DE_VEHICULO),
     * filtroDatoRellenados con dato no vacío), se construye una query dinámica para filtrar.
     * En caso contrario, se devuelven los primeros 30 productos.
     */
    public Flux<Producto> getProductSearched(SearchProductGui spg) {
        if (spg == null) {
            log.warn("SearchProductGui es null, devolviendo los primeros 30 productos por defecto.");
            return productoRepository.findFirst30();
        }

        // Verifica si hay algún FiltroDatoRellenado con dato no vacío
        boolean tieneFiltroDatos = false;
        if (spg.getFiltroDatoRellenados() != null && !spg.getFiltroDatoRellenados().isEmpty()) {
            tieneFiltroDatos = spg.getFiltroDatoRellenados().stream()
                    .anyMatch(fdr -> fdr.getDato() != null && !fdr.getDato().isBlank());
        }

        // Verifica si hay algún filtro relevante definido
        boolean tieneFiltros = (spg.getIdGrupo() != null && !spg.getIdGrupo().isBlank())
                || (spg.getDescripcion() != null && !spg.getDescripcion().isBlank())
                || (spg.getCodProductoOld() != null && !spg.getCodProductoOld().isBlank())
                || (spg.getCodFabricaOld() != null && !spg.getCodFabricaOld().isBlank())
                || (spg.getVehiculoSearch() != null && !spg.getVehiculoSearch().isBlank())
                || tieneFiltroDatos;

        if (tieneFiltros) {
            // Si hay filtros, utiliza la query dinámica
            return getProductByDynamicFilters(spg);
        } else {
            log.info("No se especificaron filtros dinámicos, devolviendo los primeros 30 productos.");
            return productoRepository.findFirst30();
        }
    }

    /**
     * Construye y ejecuta una query dinámica para filtrar productos basado en los campos
     * no nulos o no vacíos de SearchProductGui, incluyendo los FiltroDatoRellenado.
     *
     * Soporta diferentes tipos de búsqueda (CONTIENE, EXACTA, EMPIEZA, MAYOR_QUE, MENOR_QUE, DIFERENTE_DE)
     * y diferentes tipos de filtro (CADENA_TEXTO, NUMERICO, DICOTOMICO, COMPUESTO).
     */
    public Flux<Producto> getProductByDynamicFilters(SearchProductGui spg) {
        // Variables de configuración
        int defaultLimit = 99;
        int shortSearchLimit = 33;
        int minSearchLength = 3;

        // Calcula la longitud de cada filtro, considerando null como longitud 0
        int descLength = spg.getDescripcion() == null ? 0 : spg.getDescripcion().trim().length();
        int codProdOldLength = spg.getCodProductoOld() == null ? 0 : spg.getCodProductoOld().trim().length();
        int codFabricaOldLength = spg.getCodFabricaOld() == null ? 0 : spg.getCodFabricaOld().trim().length();
        int vehiculoLength = spg.getVehiculoSearch() == null ? 0 : spg.getVehiculoSearch().trim().length();

        // Si no se especifica grupo y alguno de los filtros tiene menos de minSearchLength caracteres, usamos el límite corto
        boolean isShortSearch = (spg.getIdGrupo() == null || spg.getIdGrupo().isBlank()) &&
                (descLength < minSearchLength || codProdOldLength < minSearchLength ||
                        codFabricaOldLength < minSearchLength || vehiculoLength < minSearchLength);

        int limit = isShortSearch ? shortSearchLimit : defaultLimit;

        StringBuilder query = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        // Inicio: se busca el nodo Producto
        query.append("MATCH (p:Producto) ");

        // Si se especifica idGrupo, se añade la relación con Grupo
        if (spg.getIdGrupo() != null && !spg.getIdGrupo().isBlank()) {
            query.append("MATCH (p)-[:PRODUCTO_PERTENECE_AL_GRUPO]->(g:Grupo {id: $idGrupo}) ");
            params.put("idGrupo", spg.getIdGrupo());
        }

        // Usamos WHERE 1=1 para simplificar la concatenación de condiciones
        query.append("WHERE 1=1 ");

        // Filtrado exacto para codProductoOld
        if (spg.getCodProductoOld() != null && !spg.getCodProductoOld().isBlank()) {
            String codProductoOldRegex = RegexUtil.buildContainsAllRegex(spg.getCodProductoOld());
            query.append("AND p.codProductoOld =~ $codProductoOld ");
            params.put("codProductoOld", codProductoOldRegex);
        }
        // Filtrado por descripción usando regex seguro
        if (spg.getDescripcion() != null && !spg.getDescripcion().isBlank()) {
            String descripcionRegex = RegexUtil.buildContainsAllRegex(spg.getDescripcion());
            query.append("AND p.descripcion =~ $descripcion ");
            params.put("descripcion", descripcionRegex);
        }
        // Filtrado para vehiculo usando regex seguro en VehiculoNombre
        if (spg.getVehiculoSearch() != null && !spg.getVehiculoSearch().isBlank()) {
            String vehiculoRegex = RegexUtil.buildContainsAllRegex(spg.getVehiculoSearch());
            query.append("AND EXISTS { ");
            query.append("  MATCH (p)-[:PRODUCTO_PARA_EL_VEHICULO]->(v:Vehiculo)-[:CON_NOMBRE_DE_VEHICULO]->(vn:VehiculoNombre) ");
            query.append("  WHERE vn.nombre =~ $vehiculoNombre ");
            query.append("} ");
            params.put("vehiculoNombre", vehiculoRegex);
        }
        // Filtrado para CodigoFabrica usando regex seguro
        if (spg.getCodFabricaOld() != null && !spg.getCodFabricaOld().isBlank()) {
            String codFabricaRegex = RegexUtil.buildContainsAllRegex(spg.getCodFabricaOld());
            query.append("AND EXISTS { ");
            query.append("  MATCH (p)-[:PRODUCTO_CON_CODIGO_FABRICA]->(cf:CodigoFabrica) ");
            query.append("  WHERE cf.codigo =~ $codigoFabrica ");
            query.append("} ");
            params.put("codigoFabrica", codFabricaRegex);
        }

        // Procesamiento de FiltroDatoRellenado
        if (spg.getFiltroDatoRellenados() != null && !spg.getFiltroDatoRellenados().isEmpty()) {
            // Agrupar FiltroDatoRellenado por fila
            Map<Integer, List<FiltroDatoRellenado>> filtroPorFila = spg.getFiltroDatoRellenados().stream()
                    .filter(fdr -> fdr.getDato() != null && !fdr.getDato().isBlank())
                    .collect(Collectors.groupingBy(FiltroDatoRellenado::getFila));

            if (!filtroPorFila.isEmpty()) {
                log.info("Procesando {} filas de FiltroDatoRellenado", filtroPorFila.size());

                // Construir la parte de la query para los filtros
                StringBuilder filtrosQuery = new StringBuilder();
                int filaIndex = 0;

                for (Map.Entry<Integer, List<FiltroDatoRellenado>> entry : filtroPorFila.entrySet()) {
                    Integer fila = entry.getKey();
                    List<FiltroDatoRellenado> filtrosDeFila = entry.getValue();

                    if (!filtrosDeFila.isEmpty()) {
                        // Si no es la primera fila, unimos con OR (UNION)
                        if (filaIndex > 0) {
                            filtrosQuery.append(" OR ");
                        }

                        // Inicio de la condición para esta fila
                        filtrosQuery.append("(");

                        // Para cada filtro en la fila, creamos una condición
                        for (int i = 0; i < filtrosDeFila.size(); i++) {
                            FiltroDatoRellenado fdr = filtrosDeFila.get(i);
                            String paramName = "filtro_" + fila + "_" + i;

                            // Si no es el primer filtro de la fila, intersecamos con AND
                            if (i > 0) {
                                filtrosQuery.append(" AND ");
                            }

                            // Obtenemos el tipo de filtro para determinar qué campo usar
                            // Si es null o vacío, asumimos CADENA_TEXTO por defecto
                            String campoDato = "datoString"; // Valor por defecto

                            // Verificamos el tipo de filtro para determinar qué campo usar
                            if (fdr.getFiltro() != null && fdr.getFiltro().getTipo() != null) {
                                switch (fdr.getFiltro().getTipo()) {
                                    case NUMERICO:
                                        // Para filtros numéricos, usamos el campo datoNumerico
                                        campoDato = "datoNumerico";
                                        break;
                                    case DICOTOMICO:
                                        // Para filtros dicotómicos, usamos el campo datoDicotomico
                                        campoDato = "datoDicotomico";
                                        break;
                                    case COMPUESTO:
                                        // Para filtros compuestos, usamos el campo datoCompuesto
                                        campoDato = "datoCompuesto";
                                        break;
                                    case CADENA_TEXTO:
                                    case OPCION_MULTIPLE:
                                    default:
                                        // Para texto y opción múltiple, usamos datoString (ya asignado por defecto)
                                        break;
                                }
                            }

                            // Determinamos el tipo de búsqueda a aplicar
                            TipoBusqueda tipoBusqueda = fdr.getTipoBusqueda();

                            // Condición: existe un camino desde el producto a un atributo con el filtro especificado
                            filtrosQuery.append("EXISTS { ")
                                    .append("MATCH (p)-[:PRODUCTO_CON_ATRIBUTO]->(atrib:Atributo)-[:PERTENECE_AL_FILTRO]->(:Filtro {id: $").append(paramName).append("_filtroId}) ");

                            // Agregar el ID del filtro como parámetro
                            params.put(paramName + "_filtroId", fdr.getFiltro().getId().toString());

                            // Aplicamos diferentes condiciones según el tipo de búsqueda y el tipo de dato
                            boolean esNumerico = campoDato.equals("datoNumerico");
                            boolean esComparacionNumerica = tipoBusqueda == TipoBusqueda.MAYOR_QUE ||
                                                          tipoBusqueda == TipoBusqueda.MENOR_QUE ||
                                                          tipoBusqueda == TipoBusqueda.DIFERENTE_DE;

                            if (esNumerico && esComparacionNumerica) {
                                // Para comparaciones numéricas, convertimos el dato a Double
                                try {
                                    Double valorNumerico = Double.parseDouble(fdr.getDato());
                                    params.put(paramName + "_dato", valorNumerico);

                                    // Construimos la condición de comparación numérica
                                    String operador = tipoBusqueda == TipoBusqueda.MAYOR_QUE ? " > " :
                                                    tipoBusqueda == TipoBusqueda.MENOR_QUE ? " < " : " <> ";

                                    // Aplicamos el operador correspondiente
                                    filtrosQuery.append("WHERE atrib.").append(campoDato)
                                              .append(operador).append("$").append(paramName).append("_dato ");
                                } catch (NumberFormatException e) {
                                    // Si no se puede convertir a número, usamos una condición que siempre es falsa
                                    log.warn("No se pudo convertir '{}' a número para comparación", fdr.getDato());
                                    filtrosQuery.append("WHERE false "); // Condición que nunca se cumple
                                }
                            } else {
                                // Para búsquedas basadas en texto, usamos expresiones regulares
                                String datoRegex = RegexUtil.buildRegexByTipoBusqueda(fdr.getDato(), tipoBusqueda);
                                params.put(paramName + "_dato", datoRegex);

                                // Aplicamos la condición de regex al campo correspondiente
                                filtrosQuery.append("WHERE atrib.").append(campoDato).append(" =~ $").append(paramName).append("_dato ");
                            }

                            // Cerramos la condición EXISTS
                            filtrosQuery.append("}");
                        }

                        // Cierre de la condición para esta fila
                        filtrosQuery.append(")");
                        filaIndex++;
                    }
                }

                // Si hay condiciones de filtro, las agregamos a la query principal
                if (filtrosQuery.length() > 0) {
                    query.append("AND (").append(filtrosQuery).append(") ");
                }
            }
        }

        // Finaliza la query devolviendo solo los IDs de los productos que coinciden con los criterios
        query.append("RETURN p.id as id ");
        query.append("LIMIT ").append(limit);

        final String finalQuery = query.toString();
        log.info("Dynamic Query: {}", finalQuery);
        log.debug("Parameters: {}", params); // Cambiado a debug para reducir logs en producción
        log.info("Usando límite de: {}", limit);

        // Ejecutamos la query para obtener los IDs de los productos y luego cargamos los productos completos
        return neo4jClient.query(finalQuery)
                .bindAll(params)
                .fetchAs(String.class)
                .mappedBy((typeSystem, record) -> record.get("id").asString())
                .all()
                .collectList()
                .flatMapMany(ids -> {
                    if (ids.isEmpty()) {
                        log.info("No se encontraron productos que coincidan con los criterios de búsqueda");
                        return Flux.empty();
                    }

                    log.info("Encontrados {} productos que coinciden con los criterios. Cargando detalles...", ids.size());
                    return productoRepository.findProductosByIds(ids);
                });
    }







    /**
     * Retorna los items asociados a un Producto dado su identificador.
     */
    public Flux<Item> getItemsByProductoId(UUID productoId) {
        log.info("Obteniendo Items para Producto con id: {}", productoId);
        return itemRepository.findByProductoId(productoId);
    }

    /**
     * Actualiza el campo vehiculoSearch de un SearchProductGui.
     */
    public Mono<SearchProductGui> updateVehiculoSearch(UUID id, String nuevoVehiculoSearch) {
        return searchRepo.findById(id)
                .flatMap(gui -> {
                    gui.setVehiculoSearch(nuevoVehiculoSearch);
                    return searchRepo.save(gui);
                })
                .doOnNext(gui -> {
                    log.info("Actualizado vehiculoSearch de SearchProductGui con id: {}", id);
                    updateSink.tryEmitNext(gui);
                });
    }

    /**
     * Actualiza el grupo asignado a un SearchProductGui y reinicializa la lista de filtros.
     * Si idGrupo es null, limpia la asignación y vacía la lista de filtros.
     */
    public Mono<SearchProductGui> updateGrupo(UUID id, String idGrupo) {
        return searchRepo.findById(id)
                .flatMap(gui -> {
                    if (idGrupo == null) {
                        gui.setIdGrupo(null);
                        if (gui.getFiltroDatoRellenados() != null) {
                            gui.getFiltroDatoRellenados().clear();
                        }
                        log.info("Limpieza del grupo y filtros para SearchProductGui con id: {}", id);
                        return searchRepo.save(gui);
                    } else {
                        gui.setIdGrupo(idGrupo);
                        return grupoRepo.findById(idGrupo)
                                .flatMap(grupo -> {
                                    // Ordena los filtros del grupo y crea nuevos filtros vacíos para el SearchProductGui
                                    List<GrupoFiltroRelation> relacionesOrdenadas = grupo.getFiltros().stream()
                                            .sorted(Comparator.comparing(GrupoFiltroRelation::getOrden, Comparator.nullsFirst(Integer::compareTo)))
                                            .collect(Collectors.toList());
                                    List<FiltroDatoRellenado> nuevosFiltros = new ArrayList<>();
                                    for (int i = 0; i < relacionesOrdenadas.size(); i++) {
                                        GrupoFiltroRelation rel = relacionesOrdenadas.get(i);
                                        FiltroDatoRellenado filtro = new FiltroDatoRellenado();
                                        filtro.setFiltro(rel.getFiltro());
                                        filtro.setFila(0);
                                        filtro.setColumna(i);
                                        filtro.setDato(null);
                                        nuevosFiltros.add(filtro);
                                    }
                                    gui.setFiltroDatoRellenados(nuevosFiltros);
                                    return searchRepo.save(gui);
                                });
                    }
                })
                .doOnNext(gui -> {
                    log.info("Actualizado grupo de SearchProductGui con id: {} a idGrupo: {}", id, idGrupo);
                    updateSink.tryEmitNext(gui);
                });
    }

    /**
     * Duplica una fila de filtros en un SearchProductGui.
     */
    public Mono<SearchProductGui> duplicateFiltroGroupRow(UUID id, int rowIndex) {
        return searchRepo.findById(id)
                .flatMap(gui -> {
                    List<FiltroDatoRellenado> filtros = gui.getFiltroDatoRellenados();
                    if (filtros == null || filtros.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("No hay filtros cargados"));
                    }
                    // Filtra la fila a duplicar
                    List<FiltroDatoRellenado> filaOriginal = filtros.stream()
                            .filter(f -> f.getFila() != null && f.getFila() == rowIndex)
                            .collect(Collectors.toList());
                    if (filaOriginal.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("Fila no encontrada"));
                    }
                    // Calcula el índice para la nueva fila duplicada
                    int nuevaFila = filtros.stream()
                            .mapToInt(f -> f.getFila() != null ? f.getFila() : 0)
                            .max()
                            .orElse(0) + 1;
                    // Duplica cada filtro de la fila original con el nuevo índice
                    List<FiltroDatoRellenado> duplicados = filaOriginal.stream()
                            .map(f -> {
                                FiltroDatoRellenado copia = new FiltroDatoRellenado();
                                copia.setFiltro(f.getFiltro());
                                copia.setFila(nuevaFila);
                                copia.setColumna(f.getColumna());
                                copia.setDato(null);
                                return copia;
                            })
                            .collect(Collectors.toList());
                    filtros.addAll(duplicados);
                    return searchRepo.save(gui);
                })
                .doOnNext(gui -> {
                    log.info("Duplicada la fila {} en SearchProductGui con id: {}", rowIndex, id);
                    updateSink.tryEmitNext(gui);
                });
    }

    /**
     * Actualiza el dato de un filtro específico en un SearchProductGui.
     *
     * @param id ID del SearchProductGui
     * @param rowIndex Índice de la fila del filtro
     * @param columnIndex Índice de la columna del filtro
     * @param nuevoDato Nuevo valor para el dato del filtro
     * @return Mono con el SearchProductGui actualizado
     */
    public Mono<SearchProductGui> updateFiltroDato(UUID id, int rowIndex, int columnIndex, String nuevoDato) {
        return searchRepo.findById(id)
                .flatMap(gui -> {
                    List<FiltroDatoRellenado> filtros = gui.getFiltroDatoRellenados();
                    if (filtros == null || filtros.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("No hay filtros cargados"));
                    }
                    // Busca el filtro específico por fila y columna
                    return Mono.justOrEmpty(
                            filtros.stream()
                                    .filter(f -> f.getFila() != null && f.getFila() == rowIndex &&
                                            f.getColumna() != null && f.getColumna() == columnIndex)
                                    .findFirst()
                    ).flatMap(filtro -> {
                        filtro.setDato(nuevoDato);
                        return searchRepo.save(gui);
                    });
                })
                .doOnNext(gui -> {
                    log.info("Actualizado dato del filtro en fila {} y columna {} de SearchProductGui con id: {}",
                            rowIndex, columnIndex, id);
                    updateSink.tryEmitNext(gui);
                });
    }

    /**
     * Actualiza el tipo de búsqueda de un filtro específico en un SearchProductGui.
     * Valida que el tipo de búsqueda sea compatible con el tipo de filtro.
     *
     * @param id ID del SearchProductGui
     * @param rowIndex Índice de la fila del filtro
     * @param columnIndex Índice de la columna del filtro
     * @param tipoBusqueda Nuevo tipo de búsqueda para el filtro
     * @return Mono con el SearchProductGui actualizado
     */
    public Mono<SearchProductGui> updateFiltroTipoBusqueda(UUID id, int rowIndex, int columnIndex, TipoBusqueda tipoBusqueda) {
        return searchRepo.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("No se encontró SearchProductGui con id: " + id)))
                .flatMap(gui -> {
                    List<FiltroDatoRellenado> filtros = gui.getFiltroDatoRellenados();
                    if (filtros == null || filtros.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("No hay filtros cargados"));
                    }

                    // Busca el filtro específico por fila y columna
                    return Mono.justOrEmpty(
                            filtros.stream()
                                    .filter(f -> f.getFila() != null && f.getFila() == rowIndex &&
                                            f.getColumna() != null && f.getColumna() == columnIndex)
                                    .findFirst()
                    )
                    .switchIfEmpty(Mono.error(new IllegalArgumentException(
                            String.format("No se encontró filtro en fila %d, columna %d", rowIndex, columnIndex))))
                    .flatMap(filtro -> {
                        // Validamos que el tipo de búsqueda sea compatible con el tipo de filtro
                        if (filtro.getFiltro() != null && filtro.getFiltro().getTipo() != null) {
                            boolean esNumerico = filtro.getFiltro().getTipo().name().equals("NUMERICO");
                            boolean esComparacionNumerica = tipoBusqueda == TipoBusqueda.MAYOR_QUE ||
                                                           tipoBusqueda == TipoBusqueda.MENOR_QUE;

                            // Si es una comparación numérica pero el filtro no es numérico, mostramos una advertencia
                            if (esComparacionNumerica && !esNumerico) {
                                log.warn("Advertencia: Se está aplicando un tipo de búsqueda numérica ({}) a un filtro no numérico ({})",
                                        tipoBusqueda, filtro.getFiltro().getTipo());
                            }
                        }

                        // Actualizamos el tipo de búsqueda
                        filtro.setTipoBusqueda(tipoBusqueda);
                        return searchRepo.save(gui);
                    });
                })
                .doOnNext(gui -> {
                    log.info("Actualizado tipo de búsqueda del filtro en fila {} y columna {} de SearchProductGui con id: {} a {}",
                            rowIndex, columnIndex, id, tipoBusqueda);
                    updateSink.tryEmitNext(gui);
                })
                .doOnError(e -> {
                    log.error("Error al actualizar tipo de búsqueda del filtro: {}", e.getMessage());
                });
    }

    /**
     * Devuelve sugerencias de descripción (hasta 30) basadas en el SearchProductGui recibido.
     * Si spg.idGrupo está definido, se filtrarán los productos que pertenezcan a ese grupo.
     * Se utiliza buildContainsAllRegex para construir un patrón seguro con spg.descripcion.
     *
     * @param spg Objeto de búsqueda con filtros.
     * @return Flux con hasta 30 descripciones sugeridas.
     */
    public Flux<String> productoDescripcionSugerencias(SearchProductGui spg) {
        // Genera el patrón regex seguro basado en el campo "descripcion"
        String descripcionRegex = RegexUtil.buildContainsAllRegex(spg.getDescripcion());
        log.info("Buscando sugerencias de descripción con regex: {}", descripcionRegex);

        // Si se especifica un idGrupo, lo usamos como filtro
        if (spg.getIdGrupo() != null && !spg.getIdGrupo().isBlank()) {
            log.info("Filtrando sugerencias por idGrupo: {}", spg.getIdGrupo());
            return productoRepository.findDescriptionsByRegexAndGrupo(descripcionRegex, spg.getIdGrupo());
        } else {
            // Si no hay grupo, pasamos null o cadena vacía para omitir el filtro
            return productoRepository.findDescriptionsByRegexAndGrupo(descripcionRegex, "");
        }
    }

    /**
     * Actualiza los campos comodín de un SearchProductGui: descripcion, codProductoOld y codFabricaOld.
     * Si alguno de estos campos es null o vacío, se establece como null.
     *
     * @param spg Objeto SearchProductGui con los nuevos valores.
     * @return Mono del SearchProductGui actualizado.
     */
    public Mono<SearchProductGui> updateComodines(SearchProductGui spg) {
        return searchRepo.findById(spg.getId())
                .flatMap(gui -> {
                    // Actualiza descripcion
                    String descripcion = spg.getDescripcion();
                    gui.setDescripcion((descripcion == null || descripcion.isBlank()) ? null : descripcion);

                    // Actualiza codProductoOld
                    String codProductoOld = spg.getCodProductoOld();
                    gui.setCodProductoOld((codProductoOld == null || codProductoOld.isBlank()) ? null : codProductoOld);

                    // Actualiza codFabricaOld
                    String codFabricaOld = spg.getCodFabricaOld();
                    gui.setCodFabricaOld((codFabricaOld == null || codFabricaOld.isBlank()) ? null : codFabricaOld);

                    return searchRepo.save(gui);
                })
                .doOnNext(updatedGui -> {
                    log.info("Actualizados comodines en SearchProductGui con id: {}", spg.getId());
                    updateSink.tryEmitNext(updatedGui);
                });
    }

    /**
     * Elimina una fila de filtros en un SearchProductGui.
     * Solo permite eliminar filas si hay más de una fila de filtros.
     *
     * @param id ID del SearchProductGui
     * @param rowIndex Índice de la fila a eliminar
     * @return Mono del SearchProductGui actualizado
     */
    public Mono<SearchProductGui> deleteFiltroGroupRow(UUID id, int rowIndex) {
        log.info("Iniciando eliminación de fila {} en SearchProductGui con id: {}", rowIndex, id);

        return searchRepo.findById(id)
                .flatMap(gui -> {
                    List<FiltroDatoRellenado> filtros = gui.getFiltroDatoRellenados();

                    // Verificación básica: si no hay filtros, no hay nada que eliminar
                    if (filtros == null || filtros.isEmpty()) {
                        log.warn("No hay filtros para eliminar en SearchProductGui con id: {}", id);
                        return Mono.just(gui); // Devolvemos el objeto sin cambios
                    }

                    // Contar cuántas filas distintas hay
                    Set<Integer> filasDistintas = filtros.stream()
                            .map(FiltroDatoRellenado::getFila)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());

                    log.info("Filas distintas encontradas: {} en SearchProductGui con id: {}", filasDistintas, id);

                    // Si solo hay una fila o menos, no permitimos eliminarla
                    if (filasDistintas.size() <= 1) {
                        log.warn("No se puede eliminar la única fila en SearchProductGui con id: {}", id);
                        return Mono.just(gui); // Devolvemos el objeto sin cambios
                    }

                    // Verificar si la fila a eliminar existe
                    if (!filasDistintas.contains(rowIndex)) {
                        log.warn("La fila {} no existe en SearchProductGui con id: {}", rowIndex, id);
                        return Mono.just(gui); // Devolvemos el objeto sin cambios
                    }

                    // Eliminar los filtros de la fila especificada
                    List<FiltroDatoRellenado> filtrosActualizados = new ArrayList<>();
                    for (FiltroDatoRellenado filtro : filtros) {
                        if (filtro.getFila() == null || filtro.getFila() != rowIndex) {
                            filtrosActualizados.add(filtro);
                        }
                    }

                    log.info("Se eliminarán {} filtros de la fila {} en SearchProductGui con id: {}",
                            filtros.size() - filtrosActualizados.size(), rowIndex, id);

                    gui.setFiltroDatoRellenados(filtrosActualizados);
                    return searchRepo.save(gui);
                })
                .doOnNext(gui -> {
                    log.info("Fila {} eliminada con éxito en SearchProductGui con id: {}", rowIndex, id);
                    updateSink.tryEmitNext(gui);
                })
                .doOnError(e -> {
                    log.error("Error al eliminar fila {} en SearchProductGui con id: {}: {}",
                            rowIndex, id, e.getMessage(), e);
                });
    }


}
