package corp.jamaro.jamaroservidor.app.ventas.controller.gui;

import corp.jamaro.jamaroservidor.app.ventas.model.dto.MainSaleGuiDto;
import corp.jamaro.jamaroservidor.app.ventas.service.gui.MainSaleGuiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Controller
@RequiredArgsConstructor
@Slf4j
public class MainSaleGuiController {

    private final MainSaleGuiService mainSaleGuiService;

    @MessageMapping("mainSaleGui.getByUser")
    public Mono<MainSaleGuiDto> getMainSaleGuiOfUser(String username) {
        log.info("Fetching MainSaleGui for user: {}", username);
        return mainSaleGuiService.getMainSaleGuiOfUser(username);
    }

    @MessageMapping("mainSaleGui.subscribe")
    public Flux<MainSaleGuiDto> subscribeToChanges(UUID mainSaleGuiId) {
        log.info("Subscribing to MainSaleGui changes for id: {}", mainSaleGuiId);
        return mainSaleGuiService.subscribeToChanges(mainSaleGuiId);
    }

    @MessageMapping("mainSaleGui.createSaleGui")
    public Mono<Void> createNewSaleGui(UUID mainSaleGuiId) {
        log.info("Creating new SaleGui for MainSaleGui with id: {}", mainSaleGuiId);
        return mainSaleGuiService.createNewSaleGui(mainSaleGuiId);
    }

    @MessageMapping("mainSaleGui.addSaleGui")
    public Mono<Void> addSaleGui(AddSaleGuiRequest request) {
        log.info("Adding existing SaleGui with id: {} to MainSaleGui with id: {}",
                request.saleGuiId(), request.mainSaleGuiId());
        return mainSaleGuiService.addSaleGui(request.mainSaleGuiId(), request.saleGuiId());
    }

    @MessageMapping("mainSaleGui.removeSaleGui")
    public Mono<Void> removeSaleGui(RemoveSaleGuiRequest request) {
        log.info("Removing SaleGui with id: {} from MainSaleGui with id: {}",
                request.saleGuiId(), request.mainSaleGuiId());
        return mainSaleGuiService.removeSaleGui(request.mainSaleGuiId(), request.saleGuiId());
    }

    @MessageMapping("mainSaleGui.reorder")
    public Mono<Void> reorderSaleGui(ReorderSaleGuiRequest request) {
        log.info("Reordering SaleGui with id: {} in MainSaleGui with id: {} to position: {}",
                request.saleGuiId(), request.mainSaleGuiId(), request.newPosition());
        return mainSaleGuiService.reorderSaleGuis(request.mainSaleGuiId(), request.saleGuiId(), request.newPosition());
    }

    // Records para agrupar los parámetros de las solicitudes
    public record AddSaleGuiRequest(UUID mainSaleGuiId, UUID saleGuiId) {}
    public record RemoveSaleGuiRequest(UUID mainSaleGuiId, UUID saleGuiId) {}
    public record ReorderSaleGuiRequest(UUID mainSaleGuiId, UUID saleGuiId, int newPosition) {}
}
