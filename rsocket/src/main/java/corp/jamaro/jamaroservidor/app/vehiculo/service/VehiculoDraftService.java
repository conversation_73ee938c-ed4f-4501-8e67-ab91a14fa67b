package corp.jamaro.jamaroservidor.app.vehiculo.service;

import corp.jamaro.jamaroservidor.app.vehiculo.model.Vehiculo;
import corp.jamaro.jamaroservidor.app.vehiculo.model.draft.VehiculoDraft;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.VehiculoRepository;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.draft.VehiculoDraftRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class VehiculoDraftService {

    private final VehiculoDraftRepository draftRepository;
    private final VehiculoRepository vehiculoRepository;
    private final Sinks.Many<VehiculoDraft> draftUpdatesSink = Sinks.many().replay().latest();

    public Mono<VehiculoDraft> createDraft(VehiculoDraft draft) {
        return draftRepository.save(draft)
                .doOnNext(draftUpdatesSink::tryEmitNext);
    }

    public Mono<VehiculoDraft> updateDraft(UUID id, VehiculoDraft draft) {
        return draftRepository.findById(id)
                .flatMap(existing -> {
                    draft.setId(id);
                    return draftRepository.save(draft);
                })
                .doOnNext(draftUpdatesSink::tryEmitNext);
    }

    public Mono<Void> deleteDraft(UUID id) {
        return draftRepository.deleteById(id);
    }

    public Flux<VehiculoDraft> getDraftUpdates(UUID id) {
        return draftUpdatesSink.asFlux()
                .filter(updatedDraft -> updatedDraft.getId().equals(id))
                .mergeWith(draftRepository.findById(id).flux());
    }

    public Mono<Vehiculo> convertToVehiculo(UUID draftId) {
        return draftRepository.findById(draftId)
                .flatMap(this::convertDraftToVehiculo)
                .flatMap(vehiculoRepository::save)
                .flatMap(vehiculo -> draftRepository.deleteById(draftId).thenReturn(vehiculo));
    }

    private Mono<Vehiculo> convertDraftToVehiculo(VehiculoDraft draft) {
        Vehiculo vehiculo = new Vehiculo();
        // Copiar todas las propiedades del draft al vehículo
        vehiculo.setNombres(draft.getNombres());
        vehiculo.setVehiculoMarca(draft.getVehiculoMarca());
        vehiculo.setVehiculoModelo(draft.getVehiculoModelo());
        vehiculo.setVehiculoAnios(draft.getVehiculoAnios());
        vehiculo.setVehiculoMotor(draft.getVehiculoMotor());
        vehiculo.setVehiculoCilindrada(draft.getVehiculoCilindrada());
        vehiculo.setVehiculoVersion(draft.getVehiculoVersion());
        vehiculo.setVehiculoCarroceria(draft.getVehiculoCarroceria());
        vehiculo.setVehiculoTraccion(draft.getVehiculoTraccion());
        vehiculo.setVehiculoTransmision(draft.getVehiculoTransmision());
        vehiculo.setFiles(draft.getFiles());
        vehiculo.setCreadoActualizado(Instant.now());
        vehiculo.setUser(draft.getUser());

        return Mono.just(vehiculo);
    }
}