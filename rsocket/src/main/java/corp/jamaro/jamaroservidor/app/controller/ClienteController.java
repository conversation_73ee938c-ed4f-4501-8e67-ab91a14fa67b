package corp.jamaro.jamaroservidor.app.controller;

import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.service.ClienteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;

import java.util.UUID;

/**
 * Controlador para gestionar las operaciones relacionadas con los clientes a través de RSocket.
 * Implementa búsquedas por nombre/apellido/razónSocial y por documentos (dni/ruc/otroDocumento).
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class ClienteController {

    private final ClienteService clienteService;

    /**
     * Permite suscribirse a actualizaciones de un Cliente específico.
     *
     * @param clienteId ID del Cliente al que se quiere suscribir
     * @return Flux que emite el Cliente actual y sus actualizaciones futuras
     */
    @MessageMapping("cliente.subscribe")
    public Flux<Cliente> subscribeToClienteUpdates(UUID clienteId) {
        log.info("Suscribiendo a actualizaciones del Cliente con ID: {}", clienteId);
        return clienteService.subscribeToClienteUpdates(clienteId);
    }

    /**
     * Busca clientes usando expresión regular en nombre, apellido o razónSocial.
     * Búsqueda insensible a mayúsculas y minúsculas, sin importar posición.
     * Limitado a 30 resultados para optimizar rendimiento.
     *
     * @param request Solicitud con el término de búsqueda
     * @return Flux de hasta 30 clientes que coinciden con la búsqueda
     */
    @MessageMapping("cliente.searchByName")
    public Flux<Cliente> searchClientesByName(SearchClientesByNameRequest request) {
        log.info("Buscando clientes por nombre/apellido/razónSocial con término: {}", request.searchTerm());
        return clienteService.searchClientesByNameRegex(request.searchTerm());
    }

    /**
     * Busca clientes por coincidencia exacta en dni, ruc o otroDocumento.
     * Búsqueda insensible a mayúsculas y minúsculas.
     *
     * @param request Solicitud con el documento a buscar
     * @return Flux de clientes que coinciden exactamente con el documento
     */
    @MessageMapping("cliente.searchByDocument")
    public Flux<Cliente> searchClientesByDocument(SearchClientesByDocumentRequest request) {
        log.info("Buscando clientes por documento exacto: {}", request.document());
        return clienteService.searchClientesByDocumentExact(request.document());
    }

    // Records para los parámetros de las solicitudes

    /**
     * Datos para la solicitud de búsqueda de clientes por nombre/apellido/razónSocial.
     *
     * @param searchTerm Término de búsqueda para nombre, apellido o razónSocial
     */
    public record SearchClientesByNameRequest(String searchTerm) {}

    /**
     * Datos para la solicitud de búsqueda de clientes por documento.
     *
     * @param document Documento a buscar (dni, ruc o otroDocumento)
     */
    public record SearchClientesByDocumentRequest(String document) {}
}
