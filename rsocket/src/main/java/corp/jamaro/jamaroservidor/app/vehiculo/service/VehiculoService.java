package corp.jamaro.jamaroservidor.app.vehiculo.service;

import corp.jamaro.jamaroservidor.app.vehiculo.model.*;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.*;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Instant;
import java.util.HashSet;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Servicio principal para la administración de Vehiculo y VehiculoDraft.
 * <p>
 * - Maneja creación y edición de Vehiculo, con control de nombres duplicados.
 * - Aplica transacciones reactivas en los métodos que involucran múltiples pasos
 *   y se relacionan con la consistencia del dominio.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VehiculoService {

    // Repositorios principales
    private final VehiculoRepository vehiculoRepository;
    private final VehiculoNombreRepository vehiculoNombreRepository;
    private final VehiculoMarcaRepository vehiculoMarcaRepository;
    private final VehiculoModeloRepository vehiculoModeloRepository;
    private final VehiculoAnioRepository vehiculoAnioRepository;
    private final VehiculoMotorRepository vehiculoMotorRepository;
    private final VehiculoVersionRepository vehiculoVersionRepository;
    private final VehiculoCarroceriaRepository vehiculoCarroceriaRepository;
    private final VehiculoTraccionRepository vehiculoTraccionRepository;
    private final VehiculoTransmisionRepository vehiculoTransmisionRepository;
    private final TipoDeMotorRepository tipoDeMotorRepository;
    private final VehiculoCilindradaRepository vehiculoCilindradaRepository;

    // =========================================================================
    //                           VEHICULO
    // =========================================================================

    /**
     * Verifica si existe un Vehiculo con el ID proporcionado.
     *
     * @param vehiculoId UUID del Vehiculo a verificar.
     * @return Mono<Boolean> que emite 'true' si existe, 'false' en caso contrario.
     */
    public Mono<Boolean> existsVehiculo(UUID vehiculoId) {
        log.debug("Verificando si existe un Vehiculo con ID={}", vehiculoId);
        return vehiculoRepository.existsById(vehiculoId);
    }

    /**
     * Retorna todos los Vehiculos.
     *
     * @return Flux<Vehiculo> con todos los registros en Neo4j.
     */
    public Flux<Vehiculo> getAllVehiculos() {
        log.debug("Obteniendo todos los vehiculos...");
        return vehiculoRepository.findAll();
    }

    /**
     * Crea o edita un Vehiculo, diferenciando:
     *  - Modo creación: si vehiculo.id es null.
     *  - Modo edición: si vehiculo.id existe en DB.
     * <p>
     * Maneja:
     *  - Control de concurrencia en Vehiculo.
     *  - DataIntegrityViolationException (ej. nombres duplicados).
     *  - Reasigna el User y creadoActualizado al momento de guardar.
     *
     * @param vehiculo Instancia de Vehiculo que se desea crear/editar
     * @return Mono<Vehiculo> con la entidad guardada/actualizada.
     */
    @Transactional
    public Mono<Vehiculo> saveVehiculo(Vehiculo vehiculo) {
        return SecurityUtils.getCurrentUser()
                .switchIfEmpty(Mono.error(new RuntimeException("Usuario no autenticado.")))
                .flatMap(currentUser -> {
                    // Asignamos el User al Vehiculo
                    vehiculo.setUser(currentUser);
                    // Actualizamos la marca de tiempo
                    vehiculo.setCreadoActualizado(Instant.now());

                    // Caso creación
                    if (vehiculo.getId() == null) {
                        log.info("Creando nuevo Vehiculo (sin ID) para el usuario {}", currentUser.getId());
                        return saveVehiculoWithErrorMap(vehiculo)
                                .flatMap(savedVehiculo -> {
                                    // 1) Cometer en JaVers
                                    return auditarCambiosReactive(savedVehiculo, currentUser.getUsername())
                                            // 2) Retornar el Vehiculo
                                            .thenReturn(savedVehiculo);
                                });
                    }
                    // Caso edición
                    log.info("Editando Vehiculo con ID={}, usuario={}", vehiculo.getId(), currentUser.getId());
                    return vehiculoRepository.findById(vehiculo.getId())
                            .flatMap(existingVehiculo ->
                                    eliminarTodosLosVehiculoNombre(existingVehiculo)
                                            .then(saveVehiculoWithErrorMap(vehiculo))
                            )
                            .switchIfEmpty(Mono.defer(() -> {
                                log.info("No existía Vehiculo con ID={}, se creará uno nuevo", vehiculo.getId());
                                return saveVehiculoWithErrorMap(vehiculo);
                            }))
                            .flatMap(savedVehiculo ->
                                    auditarCambiosReactive(savedVehiculo, currentUser.getUsername())
                                            .thenReturn(savedVehiculo)
                            );
                });
    }

    //eliminar este metodo y su uso.
    private Mono<Void> auditarCambiosReactive(Vehiculo vehiculo, String autor) {
        return Mono.fromCallable(() -> {
                    //javers.commit(autor, vehiculo); ya no existe
                    return (Void) null; // forzamos a 'Void'
                })
                .subscribeOn(Schedulers.boundedElastic());
    }




    /**
     * Encapsula la lógica de guardado de Vehiculo, manejando:
     *  - DataIntegrityViolationException (ej. nombre duplicado)
     *  - Problemas de concurrencia
     */
    private Mono<Vehiculo> saveVehiculoWithErrorMap(Vehiculo vehiculo) {
        log.info("Guardando Vehiculo (ID={})", vehiculo.getId());
        return vehiculoRepository.save(vehiculo)
                .doOnSuccess(saved -> log.info("Vehiculo guardado con éxito. ID={}",
                        saved.getId()))
                // Ya no hay manejo de OptimisticLockingFailureException porque se eliminó el campo Version
                .onErrorMap(DataIntegrityViolationException.class, ex -> {
                    log.error("DataIntegrityViolation al guardar Vehiculo: {}", ex.getMessage());
                    if (ex.getMessage() != null && ex.getMessage().contains("ConstraintValidationFailed")) {
                        // Intentamos extraer el valor conflictivo
                        String conflictValue = "desconocido";
                        Matcher matcher = Pattern.compile("property `nombre` = '([^']*)'")
                                .matcher(ex.getMessage());
                        if (matcher.find()) {
                            conflictValue = matcher.group(1);
                        }
                        return new RuntimeException(
                                "El nombre de vehiculo '" + conflictValue + "' ya existe.", ex);
                    }
                    return ex;
                });
    }

    /**
     * Elimina todos los VehiculoNombre de un Vehiculo existente.
     */
    private Mono<Void> eliminarTodosLosVehiculoNombre(Vehiculo existingVehiculo) {
        if (existingVehiculo.getNombres() == null || existingVehiculo.getNombres().isEmpty()) {
            return Mono.empty();
        }
        log.debug("Eliminando todos los nombres del Vehiculo ID={}", existingVehiculo.getId());
        return Flux.fromIterable(existingVehiculo.getNombres())
                .flatMap(vn -> vehiculoNombreRepository.deleteById(vn.getId()))
                .then();
    }

    /**
     * Retorna un Vehiculo encontrado por el ID de un VehiculoNombre relacionado.
     */
    public Mono<Vehiculo> getVehiculoByVehiculoNombreId(UUID vehiculoNombreId) {
        log.debug("Buscando Vehiculo por VehiculoNombreId={}", vehiculoNombreId);
        return vehiculoRepository.findByVehiculoNombreId(vehiculoNombreId)
                .flatMap(vehiculo -> vehiculoRepository.findById(vehiculo.getId()));
    }

    /**
     * Elimina un Vehiculo por su UUID.
     */
    public Mono<Void> deleteVehiculoById(UUID vehiculoId) {
        log.info("Eliminando Vehiculo ID={}", vehiculoId);
        return vehiculoRepository.deleteById(vehiculoId);
    }

    /**
     * Obtiene (o reconstruye) el "draft" de Vehiculo para un usuario.
     * - Elimina cualquier Vehiculo "incompleto" anterior
     * - Crea uno nuevo y lo retorna
     */
    @Transactional
    public Mono<Vehiculo> getDraftVehiculo() {
        return SecurityUtils.getCurrentUser()
                .flatMap(user ->
                        // 1) obtenemos SÓLO los IDs de vehículos “incompletos” de ese user
                        vehiculoRepository.findIncompleteVehiculoIdsByUserId(user.getId())
                                // 2) por cada ID, recuperamos el Vehiculo completo con findById
                                .flatMap(vehiculoRepository::findById)
                                // 3) eliminamos files y el vehículo
                                .flatMap(veh -> {
                                    if (veh.getFiles() != null) {
                                        return Flux.fromIterable(veh.getFiles())
                                                .flatMap(rel -> {
                                                    var bucketFile = rel.getBucketFile();
                                                    if (bucketFile != null && bucketFile.getId() != null) {
                                                        // eliminamos el BucketFile
                                                    }
                                                    return Mono.empty();
                                                })
                                                .then(vehiculoRepository.delete(veh));
                                    } else {
                                        return vehiculoRepository.delete(veh);
                                    }
                                })
                                // 4) cuando terminamos de borrar todos los “incompletos”,
                                // devolvemos el user “intacto” para la siguiente operación
                                .then(Mono.just(user))
                )
                // 5) Acá tenemos el user, creamos el nuevo Vehiculo "draft"
                .flatMap(u -> {
                    var nuevoVeh = new Vehiculo();
                    nuevoVeh.setUser(u);
                    nuevoVeh.setCreadoActualizado(Instant.now());
                    return vehiculoRepository.save(nuevoVeh);
                });
    }


    // =========================================================================
    //                         VEHICULO NOMBRE
    // =========================================================================

    public Mono<VehiculoNombre> saveVehiculoNombre(VehiculoNombre nombre) {
        if (nombre.getNombre() != null) {
            nombre.setNombre(nombre.getNombre().toLowerCase());
        }
        log.debug("Guardando VehiculoNombre...");
        return vehiculoNombreRepository.save(nombre);
    }

    public Flux<VehiculoNombre> getAllVehiculoNombres() {
        log.debug("Obteniendo todos los VehiculoNombre...");
        return vehiculoNombreRepository.findAll();
    }

    public Flux<VehiculoNombre> getVehiculoNombresByVehiculoId(UUID vehiculoId) {
        log.debug("Obteniendo nombres del Vehiculo ID={}", vehiculoId);
        return vehiculoRepository.findById(vehiculoId)
                .flatMapMany(vehiculo -> {
                    if (vehiculo.getNombres() == null) {
                        return Flux.empty();
                    }
                    return Flux.fromIterable(vehiculo.getNombres());
                });
    }

    public Flux<VehiculoNombre> searchVehiculoNombre(String searchTerm) {
        if (searchTerm == null || searchTerm.isBlank()) {
            return Flux.empty();
        }
        String[] tokens = searchTerm.trim().split("\\s+");

        // Construir la expresión regular:
        // (?i) => ignora mayús/minús
        // (?=.*TOKEN) => para cada palabra
        // .* => final libre
        StringBuilder patternBuilder = new StringBuilder("(?i)");
        for (String token : tokens) {
            patternBuilder.append("(?=.*").append(Pattern.quote(token)).append(")");
        }
        patternBuilder.append(".*");

        log.debug("Buscando VehiculoNombre con regex={}", patternBuilder);
        return vehiculoNombreRepository.findByNombreRegex(patternBuilder.toString());
    }

    public Mono<Void> deleteVehiculoNombreById(UUID nombreId) {
        log.info("Eliminando VehiculoNombre ID={}", nombreId);
        return vehiculoNombreRepository.findById(nombreId)
                .switchIfEmpty(Mono.error(new RuntimeException(
                        "VehiculoNombre no existe en la BD con id=" + nombreId
                )))
                .flatMap(vehiculoNombreRepository::delete);
    }

    // =========================================================================
    //                         VEHICULO MARCA
    // =========================================================================

    public Flux<VehiculoMarca> getAllVehiculoMarcas() {
        log.debug("Obteniendo todas las Marcas...");
        return vehiculoMarcaRepository.findAll();
    }

    public Mono<VehiculoMarca> saveVehiculoMarca(VehiculoMarca marca) {
        if (marca.getMarca() != null) {
            marca.setMarca(marca.getMarca().toLowerCase());
        }
        log.debug("Guardando Marca {}", marca);
        return vehiculoMarcaRepository.save(marca)
                .onErrorMap(DataIntegrityViolationException.class, ex -> {
                    if (ex.getMessage() != null && ex.getMessage().contains("ConstraintValidationFailed")) {
                        return new RuntimeException(
                                "No se puede guardar la marca '" + marca.getMarca() + "': " +
                                        "ya existe en la base de datos.", ex);
                    }
                    return ex;
                });
    }

    public Mono<Void> deleteVehiculoMarcaById(UUID marcaId) {
        log.info("Eliminando Marca con ID={}", marcaId);
        return vehiculoMarcaRepository.findById(marcaId)
                .flatMap(marca -> {
                    boolean tieneModelos = marca.getModelos() != null && !marca.getModelos().isEmpty();
                    boolean tieneMotores = marca.getMotores() != null && !marca.getMotores().isEmpty();
                    boolean tieneVersiones = marca.getVersiones() != null && !marca.getVersiones().isEmpty();

                    if (tieneModelos || tieneMotores || tieneVersiones) {
                        return Mono.error(new RuntimeException(
                                "No se puede eliminar la marca porque aún tiene relaciones con modelos/motores/versiones. "
                                        + "Elimine o reubíquelas primero."));
                    }
                    return vehiculoMarcaRepository.deleteById(marcaId);
                });
    }

    // =========================================================================
    //                         VEHICULO MODELO
    // =========================================================================

    public Mono<VehiculoModelo> saveVehiculoModelo(VehiculoModelo modelo) {
        if (modelo.getModelo() != null) {
            modelo.setModelo(modelo.getModelo().toLowerCase());
        }
        log.debug("Guardando Modelo {}", modelo);
        return vehiculoModeloRepository.save(modelo)
                .onErrorMap(DataIntegrityViolationException.class, ex -> {
                    if (ex.getMessage() != null && ex.getMessage().contains("ConstraintValidationFailed")) {
                        return new RuntimeException(
                                "No se puede guardar el modelo '" + modelo.getModelo() + "': " +
                                        "ya existe en la base de datos.", ex);
                    }
                    return ex;
                });
    }

    public Mono<Void> deleteVehiculoModeloById(UUID modeloId) {
        log.info("Eliminando Modelo con ID={}", modeloId);
        return vehiculoModeloRepository.deleteById(modeloId);
    }

    public Flux<VehiculoModelo> getModelosByMarcaId(UUID marcaId) {
        log.debug("Obteniendo modelos de la Marca ID={}", marcaId);
        return vehiculoMarcaRepository.findById(marcaId)
                .flatMapMany(marca -> {
                    if (marca.getModelos() == null) {
                        return Flux.empty();
                    }
                    return Flux.fromIterable(marca.getModelos());
                });
    }

    public Mono<VehiculoMarca> addVehiculoModeloToMarca(UUID marcaId, VehiculoModelo modelo) {
        return saveVehiculoModelo(modelo)
                .flatMap(savedModelo ->
                        vehiculoMarcaRepository.findById(marcaId)
                                .flatMap(marca -> {
                                    if (marca.getModelos() == null) {
                                        marca.setModelos(new HashSet<>());
                                    }
                                    marca.getModelos().add(savedModelo);
                                    return vehiculoMarcaRepository.save(marca);
                                })
                );
    }

    // =========================================================================
    //                         VEHICULO MOTOR
    // =========================================================================

    public Mono<VehiculoMotor> saveVehiculoMotor(VehiculoMotor motor) {
        if (motor.getMotor() != null) {
            motor.setMotor(motor.getMotor().toLowerCase());
        }
        log.debug("Guardando Motor {}", motor);
        return vehiculoMotorRepository.save(motor)
                .onErrorMap(DataIntegrityViolationException.class, ex -> {
                    if (ex.getMessage() != null && ex.getMessage().contains("ConstraintValidationFailed")) {
                        return new RuntimeException(
                                "No se puede guardar el motor '" + motor.getMotor() + "': " +
                                        "ya existe en la base de datos.", ex);
                    }
                    return ex;
                });
    }

    public Mono<Void> deleteVehiculoMotorById(UUID motorId) {
        log.info("Eliminando Motor con ID={}", motorId);
        return vehiculoMotorRepository.deleteById(motorId);
    }

    public Flux<VehiculoMotor> getMotoresByMarcaId(UUID marcaId) {
        log.debug("Obteniendo motores de la Marca ID={}", marcaId);
        return vehiculoMarcaRepository.findById(marcaId)
                .flatMapMany(marca -> {
                    if (marca.getMotores() == null) {
                        return Flux.empty();
                    }
                    return Flux.fromIterable(marca.getMotores());
                });
    }

    public Mono<VehiculoMarca> addVehiculoMotorToMarca(UUID marcaId, VehiculoMotor motor) {
        return saveVehiculoMotor(motor)
                .flatMap(savedMotor ->
                        vehiculoMarcaRepository.findById(marcaId)
                                .flatMap(marca -> {
                                    if (marca.getMotores() == null) {
                                        marca.setMotores(new HashSet<>());
                                    }
                                    marca.getMotores().add(savedMotor);
                                    return vehiculoMarcaRepository.save(marca);
                                })
                );
    }

    // =========================================================================
    //                         TIPO DE MOTOR
    // =========================================================================

    public Flux<TipoDeMotor> getAllTipoDeMotor() {
        log.debug("Obteniendo todos los Tipos de Motor...");
        return tipoDeMotorRepository.findAll();
    }

    // =========================================================================
    //                         VEHICULO CILINDRADA
    // =========================================================================

    public Flux<VehiculoCilindrada> getAllVehiculoCilindradas() {
        log.debug("Obteniendo todas las cilindradas...");
        return vehiculoCilindradaRepository.findAll();
    }

    // =========================================================================
    //                         VEHICULO VERSION
    // =========================================================================

    public Mono<VehiculoVersion> saveVehiculoVersion(VehiculoVersion version) {
        log.debug("Guardando Version {}", version);
        return vehiculoVersionRepository.save(version)
                .onErrorMap(DataIntegrityViolationException.class, ex -> {
                    if (ex.getMessage() != null && ex.getMessage().contains("ConstraintValidationFailed")) {
                        return new RuntimeException(
                                "No se puede guardar la versión '" + version.getVersion() + "': " +
                                        "ya existe en la base de datos.", ex);
                    }
                    return ex;
                });
    }

    public Mono<Void> deleteVehiculoVersionById(UUID versionId) {
        log.info("Eliminando VehiculoVersion con ID={}", versionId);
        return vehiculoVersionRepository.deleteById(versionId);
    }

    public Flux<VehiculoVersion> getVersionesByMarcaId(UUID marcaId) {
        log.debug("Obteniendo versiones de la Marca ID={}", marcaId);
        return vehiculoMarcaRepository.findById(marcaId)
                .flatMapMany(marca -> {
                    if (marca.getVersiones() == null) {
                        return Flux.empty();
                    }
                    return Flux.fromIterable(marca.getVersiones());
                });
    }

    public Mono<VehiculoMarca> addVehiculoVersionToMarca(UUID marcaId, VehiculoVersion version) {
        return saveVehiculoVersion(version)
                .flatMap(savedVersion ->
                        vehiculoMarcaRepository.findById(marcaId)
                                .flatMap(marca -> {
                                    if (marca.getVersiones() == null) {
                                        marca.setVersiones(new HashSet<>());
                                    }
                                    marca.getVersiones().add(savedVersion);
                                    return vehiculoMarcaRepository.save(marca);
                                })
                );
    }

    // =========================================================================
    //                         VEHICULO CARROCERIA
    // =========================================================================

    public Flux<VehiculoCarroceria> getAllVehiculoCarrocerias() {
        log.debug("Obteniendo todas las carrocerías...");
        return vehiculoCarroceriaRepository.findAll();
    }

    // =========================================================================
    //                         VEHICULO TRACCION
    // =========================================================================

    public Flux<VehiculoTraccion> getAllVehiculoTracciones() {
        log.debug("Obteniendo todas las tracciones...");
        return vehiculoTraccionRepository.findAll();
    }

    // =========================================================================
    //                         VEHICULO TRANSMISION
    // =========================================================================

    public Flux<VehiculoTransmision> getAllVehiculoTransmisiones() {
        log.debug("Obteniendo todas las transmisiones...");
        return vehiculoTransmisionRepository.findAll();
    }


}
