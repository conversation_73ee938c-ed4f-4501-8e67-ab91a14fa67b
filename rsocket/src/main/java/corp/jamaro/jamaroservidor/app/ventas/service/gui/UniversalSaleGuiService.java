package corp.jamaro.jamaroservidor.app.ventas.service.gui;

import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import corp.jamaro.jamaroservidor.app.ventas.model.dto.UniversalSaleGuiDto;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.MainSaleGui;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.UniversalSaleGui;
import corp.jamaro.jamaroservidor.app.ventas.repository.gui.UniversalSaleGuiRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.HashSet;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class UniversalSaleGuiService {

    private final UniversalSaleGuiRepository guiRepository;

    /**
     * Mapa para almacenar Sinks y emitir notificaciones de UniversalSaleGuiDto
     * (en lugar de la entidad completa).
     */
    private final ConcurrentHashMap<UUID, Sinks.Many<UniversalSaleGuiDto>> sinks = new ConcurrentHashMap<>();

    /**
     * Helper para obtener o crear un Sink (de UniversalSaleGuiDto) para notificar actualizaciones.
     */
    private Sinks.Many<UniversalSaleGuiDto> getOrCreateSink(UUID guiId) {
        return sinks.computeIfAbsent(guiId, id -> Sinks.many().replay().latest());
    }

    /* -------------------------------------------------------------------------
       Métodos internos
       ------------------------------------------------------------------------- */

    /**
     * Emite el DTO en el Sink correspondiente.
     */
    private Mono<UniversalSaleGuiDto> emit(UniversalSaleGuiDto dto) {
        return Mono.just(dto)
                .doOnSuccess(d -> {
                    var sink = getOrCreateSink(d.getId());
                    sink.tryEmitNext(d);
                    log.info("Emitiendo DTO de UniversalSaleGui con id: {}", d.getId());
                });
    }

    /* -------------------------------------------------------------------------
       Métodos Públicos
       ------------------------------------------------------------------------- */

    /**
     * Permite suscribirse a los cambios en la GUI de un usuario,
     * emitiendo UniversalSaleGuiDto en lugar de la entidad completa.
     *
     * 1. Se obtiene (o crea) la UniversalSaleGui del usuario.
     * 2. Se proyecta a DTO usando la query optimizada del repositorio.
     * 3. Se suscribe al Sink con esa GUI.
     */
    public Flux<UniversalSaleGuiDto> subscribeToChanges(UUID userId) {
        if (userId == null) {
            return Flux.error(new RuntimeException("userId no puede ser null"));
        }

        return guiRepository.findGuiIdByUserId(userId)
                .switchIfEmpty(
                        SecurityUtils.getCurrentUser()
                                .switchIfEmpty(Mono.error(new RuntimeException("Usuario no autenticado para crear nueva GUI")))
                                .flatMap(user -> {
                                    var mainSaleGui = new MainSaleGui();
                                    mainSaleGui.setUsernameOwner(user.getUsername());

                                    var newGui = new UniversalSaleGui();
                                    newGui.setUser(user);
                                    newGui.setMainSaleGui(mainSaleGui);
                                    newGui.setAuditingMainSales(new HashSet<>());
                                    return guiRepository.save(newGui).map(UniversalSaleGui::getId);
                                })
                )
                .flatMap(guiRepository::findDtoById)
                .flatMapMany(dto -> {
                    var sink = getOrCreateSink(dto.getId());
                    // Emitimos primero el estado actual
                    return sink.asFlux().startWith(dto);
                });
    }

    /**
     * Agrega el id de un MainSaleGui a la lista de auditoría de la GUI y emite el DTO actualizado.
     * Utiliza una consulta Cypher optimizada para actualizar solo el campo necesario.
     */
    public Mono<UniversalSaleGuiDto> addAuditedMainSale(UUID universalSaleGuiId, UUID mainSaleGuiId) {
        return guiRepository.addAuditedMainSaleAndReturnDto(universalSaleGuiId, mainSaleGuiId)
                .flatMap(this::emit);
    }

    /**
     * Remueve el id de un MainSaleGui de la lista de auditoría de la GUI y emite el DTO actualizado.
     * Utiliza una consulta Cypher optimizada para actualizar solo el campo necesario.
     */
    public Mono<UniversalSaleGuiDto> removeAuditedMainSale(UUID universalSaleGuiId, UUID mainSaleGuiId) {
        return guiRepository.removeAuditedMainSaleAndReturnDto(universalSaleGuiId, mainSaleGuiId)
                .flatMap(this::emit);
    }

    /**
     * Actualiza la configuración (guiConfig) de la GUI del usuario actual y emite el DTO.
     * Utiliza una consulta Cypher optimizada para actualizar solo el campo necesario.
     */
    public Mono<UniversalSaleGuiDto> saveGuiConfig(String json) {
        return SecurityUtils.getCurrentUser()
                .switchIfEmpty(Mono.error(new RuntimeException("Usuario no autenticado")))
                .flatMap(user -> {
                    if (user.getId() == null) {
                        return Mono.error(new RuntimeException("ID de usuario no disponible"));
                    }
                    return guiRepository.findGuiIdByUserId(user.getId());
                })
                .switchIfEmpty(Mono.error(new RuntimeException("UniversalSaleGui no encontrada para el usuario")))
                .flatMap(guiId -> guiRepository.updateGuiConfigAndReturnDto(guiId, json))
                .flatMap(this::emit);
    }


}
