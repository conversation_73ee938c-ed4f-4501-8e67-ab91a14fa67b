package corp.jamaro.jamaroservidor.app.producto.service;

import corp.jamaro.jamaroservidor.app.producto.model.Producto;
import corp.jamaro.jamaroservidor.app.producto.repository.ProductoRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.SearchProductGui;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductoService {

    private final ProductoRepository productoRepository;

}
