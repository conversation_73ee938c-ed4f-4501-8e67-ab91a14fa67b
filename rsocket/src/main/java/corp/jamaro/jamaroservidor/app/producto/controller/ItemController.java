package corp.jamaro.jamaroservidor.app.producto.controller;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.service.ItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Controller
@RequiredArgsConstructor
@Slf4j
@MessageMapping("item")
public class ItemController {

    private final ItemService itemService;

    /**
     * Endpoint para obtener un Item completo por su codCompuesto.
     */
    @MessageMapping("getByCodCompuesto")
    public Mono<Item> getByCodCompuesto(@Payload String codCompuesto) {
        log.info("Solicitud para obtener Item por codCompuesto: {}", codCompuesto);
        return itemService.getItemByCodCompuesto(codCompuesto);
    }

    /**
     * Endpoint para buscar Items por descripción utilizando regex.
     */
    @MessageMapping("getByDescripcionRegex")
    public Flux<Item> getByDescripcionRegex(@Payload String descripcion) {
        log.info("Solicitud para obtener Items por descripción con patrón: {}", descripcion);
        return itemService.getItemsByDescripcionRegex(descripcion);
    }

    /**
     * Endpoint para obtener Items asociados a un Producto mediante su UUID.
     */
    @MessageMapping("getByProductoId")
    public Flux<Item> getByProductoId(@Payload UUID productoId) {
        log.info("Solicitud para obtener Items asociados al Producto con id: {}", productoId);
        return itemService.getItemsByProductoId(productoId);
    }
}
