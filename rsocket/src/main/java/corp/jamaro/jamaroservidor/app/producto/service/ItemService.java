package corp.jamaro.jamaroservidor.app.producto.service;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemService {

    private final ItemRepository itemRepository;

    /**
     * Obtiene un Item completo a partir del codCompuesto.
     */
    public Mono<Item> getItemByCodCompuesto(String codCompuesto) {
        log.info("Obteniendo Item por codCompuesto: {}", codCompuesto);
        return itemRepository.findByCodCompuesto(codCompuesto);
    }

    /**
     * Busca Items cuya descripción cumpla el regex.
     * Se construye un patrón que ignora mayúsculas/minúsculas y busca en cualquier posición.
     */
    public Flux<Item> getItemsByDescripcionRegex(String descripcion) {
        String regex = "(?i).*" + descripcion + ".*";
        log.info("Buscando Items por descripción con regex: {}", regex);
        return itemRepository.findByDescripcionRegex(regex);
    }

    /**
     * Obtiene los Items asociados a un Producto mediante su UUID.
     */
    public Flux<Item> getItemsByProductoId(UUID productoId) {
        log.info("Obteniendo Items asociados al Producto con id: {}", productoId);
        return itemRepository.findByProductoId(productoId);
    }

    /**
     * Obtiene los primeros 30 Items con todas sus relaciones.
     */
    public Flux<Item> getFirst30Items() {
        log.info("Obteniendo los primeros 30 Items");
        return itemRepository.findFirst30();
    }

    /**
     * Obtiene Items por sus IDs con todas sus relaciones.
     */
    public Flux<Item> getItemsByIds(List<String> ids) {
        log.info("Obteniendo Items por IDs: {}", ids);
        return itemRepository.findItemsByIds(ids);
    }
}
