spring.application.name=JamaroServidor

# Configuración de Neo4j
spring.neo4j.uri=bolt://192.168.1.45:7687
spring.neo4j.authentication.username=neo4j
spring.neo4j.authentication.password=NsqcpNeo4j108

# Configuración de RSocket
spring.rsocket.server.port=7000
spring.rsocket.server.transport=tcp

# Configuración de JWT
jwt.secret=tuClaveSecretaMuyLargaYSeguraParaFirmarTokensJWTtuClaveSecretaMuyLargaYSeguraParaFirmarTokensJWTtuClaveSecretaMuyLargaYSeguraParaFirmarTokensJWT
jwt.expiration=86400

# Configurar rotación de logs
#logging.file.name=logs/jamaro-server.log
#logging.logback.rollingpolicy.max-file-size=50MB
#logging.logback.rollingpolicy.max-history=30

logging.level.root=INFO
logging.level.corp.jamaro=DEBUG
logging.level.io.rsocket=ERROR
logging.level.io.rsocket.FrameLogger=OFF
logging.level.org.springframework.security=INFO
logging.level.io.jsonwebtoken=INFO
logging.level.org.springframework.rsocket=INFO


# --- Configuración de Minio ---
minio.url=http://192.168.1.45:9000
minio.access-key=admin
minio.secret-key=NsqcpMinio108
minio.bucket=jamaro-bucket