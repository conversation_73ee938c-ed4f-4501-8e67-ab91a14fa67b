package corp.jamaro.jamaroservidor.security.util;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.security.model.UserAuth;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import io.jsonwebtoken.Claims;
import reactor.core.publisher.Mono;

import java.util.Map;

@UtilityClass
public class SecurityUtils {

    /**
     * Obtiene el usuario autenticado del Security Context.
     *
     * @return Mono<User> del usuario autenticado.
     */
    public Mono<User> getCurrentUser() {
        return ReactiveSecurityContextHolder.getContext()
                .map(SecurityContext::getAuthentication)
                .filter(Authentication::isAuthenticated)
                .filter(auth -> auth.getPrincipal() instanceof UserAuth)
                .map(auth -> (UserAuth) auth.getPrincipal())
                .map(UserAuth::getUser);
    }

    /**
     * Extrae la metadata del cliente (clientType y clientVersion) a partir de los claims
     * presentes en los detalles del Authentication.
     *
     * @return Mono<Map<String, Object>> con la metadata del cliente.
     */
    public Mono<Map<String, Object>> getClientMetadata() {
        return ReactiveSecurityContextHolder.getContext()
                .map(SecurityContext::getAuthentication)
                .filter(Authentication::isAuthenticated)
                .map(auth -> {
                    // Se asume que auth.getDetails() contiene un objeto de tipo Claims
                    if (auth.getDetails() instanceof Claims) {
                        Claims claims = (Claims) auth.getDetails();
                        return Map.of(
                                "clientType", claims.get("clientType"),
                                "clientVersion", claims.get("clientVersion")
                        );
                    }
                    return Map.<String, Object>of();
                });
    }
}
