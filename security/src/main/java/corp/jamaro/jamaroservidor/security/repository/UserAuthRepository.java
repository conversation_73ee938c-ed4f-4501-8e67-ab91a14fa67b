package corp.jamaro.jamaroservidor.security.repository;

import corp.jamaro.jamaroservidor.security.model.UserAuth;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface UserAuthRepository extends ReactiveNeo4jRepository<UserAuth, String> {
    Mono<UserAuth> findByUsername(String username);
    Mono<UserAuth> findByRfid(String rfid);
}
